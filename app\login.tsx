// app/components/LoginScreen.tsx
import React, { useMemo, useState } from 'react';
import {
  View, Text, TextInput, TouchableOpacity, StyleSheet,
  Image, StatusBar, SafeAreaView, Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

const BRAND_BLUE = '#0B4A6A';
const INPUT_BLUE = '#2F80ED';
const LIGHT_BG   = '#EEF4FF';
const TOP_GAP    = 34;
const SIDE_GAP   = 16;

const EMAIL_MAX = 50;
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/i;

// AsyncStorage keys
const USERS_KEY = 'nlk_users';
const CURRENT_USER_KEY = 'nlk_current_user';

export default function LoginScreen() {
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [emailErr, setEmailErr] = useState<string | undefined>(undefined);
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
  const [loginError, setLoginError] = useState('');
  const [loading, setLoading] = useState(false);

  const isEmailValid = useMemo(() => {
    const trimmed = email.trim();
    if (!trimmed) return false;
    if (trimmed.length > EMAIL_MAX) return false;
    return emailRegex.test(trimmed);
  }, [email]);

  const onChangeEmail = (t: string) => {
    const cleaned = t.replace(/^\s+/, '').replace(/\s+/g, '').toLowerCase().slice(0, EMAIL_MAX);
    setEmail(cleaned);
    setErrors((p) => ({ ...p, email: undefined }));
    setEmailErr(undefined);
    setLoginError('');
  };

  const handleLogin = async () => {
    const newErrors: { email?: string; password?: string } = {};
    setLoginError('');
    setEmailErr(undefined);

    if (!email.trim()) {
      newErrors.email = 'E-posta alanı zorunludur.';
    } else if (email.trim().length > EMAIL_MAX) {
      newErrors.email = `E-posta en fazla ${EMAIL_MAX} karakter olmalıdır.`;
    } else if (!emailRegex.test(email.trim())) {
      newErrors.email = 'Geçerli bir e-posta girin (ör. <EMAIL>).';
    }

    if (!password.trim()) newErrors.password = 'Şifre alanı zorunludur.';

    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;

    try {
      setLoading(true);

      const raw = await AsyncStorage.getItem(USERS_KEY);
      const users: Array<{ name: string; email: string; password: string }> = raw ? JSON.parse(raw) : [];

      if (!users.length) {
        Alert.alert('Bilgi', 'Kayıtlı kullanıcı bulunamadı. Lütfen önce kayıt olun.');
        console.log('USERS boş veya yok:', raw);
        return;
      }

      const found = users.find(
        (u) => u.email?.toLowerCase() === email.toLowerCase() && u.password === password
      );

      if (!found) {
        setLoginError('E-posta veya şifre hatalı.');
        console.log('Giriş başarısız. Girilen:', { email, password }, 'Kayıtlı kullanıcılar:', users);
        return;
      }

      await AsyncStorage.setItem(CURRENT_USER_KEY, JSON.stringify(found));
      router.replace('/customer-info');
    } catch (err) {
      console.error(err);
      Alert.alert('Hata', 'Giriş sırasında bir sorun oluştu.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={BRAND_BLUE} />

      <View style={styles.headerArea}>
        <View style={styles.brandBar}>
          <Image
            source={require('../../assets/images/logoNLK.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.title}>GİRİŞ YAP</Text>

        <TextInput
          style={styles.input}
          placeholder="E-posta"
          placeholderTextColor="#6B7A90"
          value={email}
          onChangeText={onChangeEmail}
          autoCapitalize="none"
          keyboardType="email-address"
          maxLength={EMAIL_MAX}
        />
        {(errors.email || emailErr) && (
          <Text style={styles.error}>{errors.email ?? emailErr}</Text>
        )}

        <TextInput
          style={styles.input}
          placeholder="Şifre"
          placeholderTextColor="#6B7A90"
          secureTextEntry
          value={password}
          onChangeText={(t) => {
            setPassword(t);
            setErrors((p) => ({ ...p, password: undefined }));
            setLoginError('');
          }}
        />
        {errors.password && <Text style={styles.error}>{errors.password}</Text>}
        {loginError !== '' && <Text style={styles.error}>{loginError}</Text>}

        <TouchableOpacity
          style={[styles.primaryBtn, loading && { opacity: 0.7 }]}
          onPress={handleLogin}
          disabled={loading /* || !isEmailValid */ }
        >
          <Text style={styles.primaryBtnText}>{loading ? 'Kontrol ediliyor...' : 'GİRİŞ YAP'}</Text>
        </TouchableOpacity>

        <View style={styles.bottomRow}>
          <Text style={styles.bottomTextMuted}>Don’t have an account? </Text>
          <TouchableOpacity onPress={() => router.push('/register')}>
            <Text style={styles.bottomLink}>Sign up</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity onPress={() => router.push('/forgot-password')}>
          <Text style={styles.forgotPassword}>Şifremi Unuttum?</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  headerArea: { marginTop: TOP_GAP, paddingHorizontal: SIDE_GAP },
  brandBar: {
    backgroundColor: BRAND_BLUE, height: 76, borderRadius: 12,
    alignItems: 'center', justifyContent: 'center',
    shadowColor: '#000', shadowOpacity: 0.15, shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8, elevation: 4,
  },
  logo: { width: 170, height: 40 },
  content: { flex: 1, paddingHorizontal: 24, marginTop: 28 },
  title: { fontSize: 24, fontWeight: '700', textAlign: 'center', marginBottom: 24, letterSpacing: 0.6, color: '#000' },
  input: {
    backgroundColor: LIGHT_BG, borderWidth: 2, borderColor: INPUT_BLUE, borderRadius: 16,
    paddingVertical: 14, paddingHorizontal: 16, marginBottom: 16, fontSize: 16,
  },
  error: { color: '#ED1C24', fontSize: 13, marginTop: -8, marginBottom: 10, marginLeft: 6 },
  primaryBtn: { backgroundColor: INPUT_BLUE, paddingVertical: 16, borderRadius: 16, alignItems: 'center', marginTop: 8 },
  primaryBtnText: { color: '#fff', fontWeight: '700', fontSize: 18, letterSpacing: 0.6 },
  bottomRow: { flexDirection: 'row', justifyContent: 'center', marginTop: 22 },
  bottomTextMuted: { color: '#495566', fontSize: 14 },
  bottomLink: { color: INPUT_BLUE, fontSize: 14, fontWeight: '600' },
  forgotPassword: { marginTop: 10, textAlign: 'center', color: INPUT_BLUE, fontSize: 14 },
});
