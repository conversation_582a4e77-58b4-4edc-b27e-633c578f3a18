import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { Dimensions, Image, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

const { width } = Dimensions.get('window');



export default function LoginScreen() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={styles.container}>
      {/* ÜST MAVİ BAR ve LOGO */}
      <View style={styles.header}>
        <Image
          source={require('../assets/images/logo_light.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>

      {/* GİRİŞ YAP BAŞLIK */}
      <Text style={styles.title}>Giriş Yap</Text>

      {/* INPUTLAR */}
      <TextInput
        style={styles.input}
        placeholder="E-posta"
        value={email}
        onChangeText={setEmail}
        autoCapitalize="none"
        placeholderTextColor="#333"
      />
      <TextInput
        style={styles.input}
        placeholder="Şifre"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
        placeholderTextColor="#333"
      />

      {/* GİRİŞ YAP BUTONU */}
      <TouchableOpacity style={styles.button} onPress={() => router.push('/musteri-bilgileri')}>
        <Text style={styles.buttonText}>Giriş Yap</Text>
      </TouchableOpacity>

      {/* ALTTAKİ SATIR */}
      <View style={styles.row}>
        <Text style={styles.altLink1} onPress={() => router.push('/register')}>
          Hesabınız yok mu?
        </Text>
        <Text style={styles.altLink2} onPress={() => router.push('/register')}>
          {'               '}Kayıt Ol
        </Text>
      </View>

      <TouchableOpacity onPress={() => alert('Şifre sıfırlama fonksiyonu eklenebilir.')}>
        <Text style={styles.forgot}>Şifremi Unuttum</Text>
      </TouchableOpacity>
    </View>
    </>
  );
}

const HEADER_HEIGHT = 60;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 0
  },
  header: {
    width: width,
    height: HEADER_HEIGHT,
    backgroundColor: '#1a3579',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingLeft: 22,
    marginBottom: 12
  },
  logo: {
    width: 120,
    height: 38,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginVertical: 24,
    alignSelf: 'center'
  },
  input: {
    width: '100%',
    maxWidth: 340,
    height: 48,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 14,
    marginBottom: 15,
    fontSize: 16,
    backgroundColor: '#fff'
  },
  button: {
    width: '100%',
    maxWidth: 340,
    backgroundColor: '#e53935',
    borderRadius: 10,
    alignItems: 'center',
    paddingVertical: 14,
    marginTop: 5,
    marginBottom: 18,
    elevation: 2
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 19,
    letterSpacing: 0.3
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 4
  },
  altLink1: {
    color: '#e53935',
    fontWeight: 'bold',
    fontSize: 15.7
  },
  altLink2: {
    color: '#1a3579',
    fontWeight: 'bold',
    fontSize: 15.7
  },
  forgot: {
    color: '#1a3579',
    marginTop: 6,
    fontSize: 14.7,
    alignSelf: 'flex-start',
    marginLeft: 12,
    textDecorationLine: 'underline'
  }
});
