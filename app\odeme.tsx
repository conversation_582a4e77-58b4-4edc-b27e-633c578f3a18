import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { Alert, Image, Platform, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

let NFC: any = null;
if (Platform.OS !== 'web') {
  try {
    NFC = require('expo-nfc');
  } catch (e) {
    // NFC modülü <PERSON> (Expo Go'da olabilir)
    NFC = null;
  }
}

export default function OdemeScreen() {
  const router = useRouter();
  const [isim, setIsim] = useState('');
  const [kartNo, setKartNo] = useState('');
  const [ay, setAy] = useState('');
  const [yil, setYil] = useState('');
  const [ccv, setCcv] = useState('');

  const handleNfcPayment = async () => {
    if (Platform.OS === 'web' || !NFC) {
      Alert.alert('Uyarı', 'NFC sadece mobil cihazda çalışır!');
      return;
    }
    try {
      await NFC.startDetectionAsync();
      const tag = await NFC.getTagAsync();
      Alert.alert('NFC OKUNDU!', JSON.stringify(tag));
      NFC.stopDetectionAsync();
      // tag bilgisini backend'e gönderebilirsin
    } catch (e: any) {
      Alert.alert('NFC Hatası', e.message || String(e));
      NFC.stopDetectionAsync();
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
      <Image source={require('../assets/images/logo_light.png')} style={styles.logo} />
      <Text style={styles.title}>Ödeme</Text>
      <TextInput style={styles.input} placeholder="Kart Üzerindeki İsim" value={isim} onChangeText={setIsim} />
      <TextInput style={styles.input} placeholder="Kart Numarası" value={kartNo} onChangeText={setKartNo} keyboardType="numeric" />
      <View style={styles.row}>
        <TextInput style={[styles.input, {flex:1, marginRight:6}]} placeholder="Ay (AA)" value={ay} onChangeText={setAy} keyboardType="numeric" />
        <TextInput style={[styles.input, {flex:1, marginLeft:6}]} placeholder="Yıl (YY)" value={yil} onChangeText={setYil} keyboardType="numeric" />
        <TextInput style={[styles.input, {flex:1, marginLeft:6}]} placeholder="CCV" value={ccv} onChangeText={setCcv} keyboardType="numeric" />
      </View>
      <View style={styles.nfcRow}>
        <TouchableOpacity style={styles.nfcButton} onPress={handleNfcPayment} disabled={Platform.OS === 'web' || !NFC}>
          <Text style={{color:'#fff', fontWeight:'bold'}}>NFC ile Tara</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.nfcButton, {backgroundColor:'#aaa'}]} onPress={() => Alert.alert('Bu özellik eklenmedi.')}>
          <Text style={{color:'#fff'}}>Kartı Tara</Text>
        </TouchableOpacity>
      </View>
      <TouchableOpacity style={styles.payButton} onPress={() => Alert.alert('Ödeme tamamlandı!')}>
        <Text style={styles.payButtonText}>Ödeme Yap</Text>
      </TouchableOpacity>
    </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16
  },
  logo: {
    width: 160,
    height: 60,
    resizeMode: 'contain',
    marginBottom: 8
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10
  },
  input: {
    width: '100%',
    maxWidth: 340,
    height: 48,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 14,
    marginBottom: 10,
    fontSize: 16
  },
  row: {
    flexDirection: 'row',
    width: '100%',
    maxWidth: 340
  },
  nfcRow: {
    flexDirection: 'row',
    marginTop: 8,
    marginBottom: 14,
    width: '100%',
    maxWidth: 340,
    justifyContent: 'space-between'
  },
  nfcButton: {
    flex: 1,
    backgroundColor: '#1a3579',
    borderRadius: 8,
    alignItems: 'center',
    padding: 14,
    marginHorizontal: 3
  },
  payButton: {
    width: '100%',
    maxWidth: 340,
    backgroundColor: '#e53935',
    borderRadius: 8,
    alignItems: 'center',
    padding: 14,
    marginTop: 10
  },
  payButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18
  }
});
