import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { Alert, Dimensions, Image, Platform, ScrollView, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

const { width } = Dimensions.get('window');

let NFC: any = null;
if (Platform.OS !== 'web') {
  try {
    NFC = require('expo-nfc');
  } catch (e) {
    // NFC modülü y<PERSON>med<PERSON> (Expo Go'da olabilir)
    NFC = null;
  }
}

export default function OdemeScreen() {
  const router = useRouter();
  const [isim, setIsim] = useState('');
  const [kartNo, setKartNo] = useState('');
  const [ay, setAy] = useState('');
  const [yil, setYil] = useState('');
  const [ccv, setCcv] = useState('');

  const handleNfcPayment = async () => {
    if (Platform.OS === 'web' || !NFC) {
      Alert.alert('Uyarı', 'NFC sadece mobil cihazda çalışır!');
      return;
    }
    try {
      await NFC.startDetectionAsync();
      const tag = await NFC.getTagAsync();
      Alert.alert('NFC OKUNDU!', JSON.stringify(tag));
      NFC.stopDetectionAsync();
      // tag bilgisini backend'e gönderebilirsin
    } catch (e: any) {
      Alert.alert('NFC Hatası', e.message || String(e));
      NFC.stopDetectionAsync();
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="light-content" backgroundColor="#1a3579" />
      <View style={styles.container}>
        {/* ÜST MAVİ BAR ve LOGO */}
        <View style={styles.header}>
          <Image
            source={require('../assets/images/logo_light.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        {/* CONTENT AREA */}
        <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer} keyboardShouldPersistTaps="handled">
          {/* ÖDEME BAŞLIK */}
          <Text style={styles.title}>Ödeme</Text>

          {/* KART BİLGİLERİ BAŞLIK */}
          <Text style={styles.sectionTitle}>Kart Bilgileri*</Text>

          {/* KART BİLGİLERİ INPUTLARI */}
          <TextInput style={styles.input} placeholder="Kart Üzerindeki İsim" value={isim} onChangeText={setIsim} />
          <TextInput style={styles.input} placeholder="Kart Numarası" value={kartNo} onChangeText={setKartNo} keyboardType="numeric" />

          <View style={styles.row}>
            <TextInput style={[styles.input, styles.thirdInput]} placeholder="Ay (AA)" value={ay} onChangeText={setAy} keyboardType="numeric" />
            <TextInput style={[styles.input, styles.thirdInput]} placeholder="Yıl (YY)" value={yil} onChangeText={setYil} keyboardType="numeric" />
            <TextInput style={[styles.input, styles.thirdInput]} placeholder="CCV*" value={ccv} onChangeText={setCcv} keyboardType="numeric" />
          </View>

          {/* KART EKLEME YÖNTEMLERİ BAŞLIK */}
          <Text style={styles.sectionTitle}>Kart Ekleme Yöntemleri*</Text>

          <View style={styles.nfcRow}>
            <TouchableOpacity style={styles.nfcButton} onPress={handleNfcPayment} disabled={Platform.OS === 'web' || !NFC}>
              <Text style={styles.nfcIcon}>))) </Text>
              <Text style={styles.nfcText}>Nfc İle Tara</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.nfcButton, styles.cameraButton]} onPress={() => Alert.alert('Bu özellik eklenmedi.')}>
              <Text style={styles.nfcIcon}>📷 </Text>
              <Text style={styles.nfcText}>Kartı Tara</Text>
            </TouchableOpacity>
          </View>

          {/* KART GÖRSEL ALANI */}
          <View style={styles.cardPreview}>
            <View style={styles.cardHeader}>
              <View style={styles.cardChip}></View>
              <Text style={styles.visaText}>VISA</Text>
            </View>
            <Text style={styles.cardNumber}>################</Text>
            <Text style={styles.cardName}>İsim Soyisim</Text>
          </View>

          {/* ÖDEME BUTONLARI */}
          <View style={styles.paymentButtons}>
            <TouchableOpacity style={styles.payButton} onPress={() => Alert.alert('Ödeme tamamlandı!')}>
              <Text style={styles.lockIcon}>🔒 </Text>
              <Text style={styles.payButtonText}>Ödeme Yap</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.bkmButton} onPress={() => Alert.alert('BKM Express ile ödeme!')}>
              <Text style={styles.bkmText}>BKM express İLE ÖDE</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const HEADER_HEIGHT = 100;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    width: width,
    height: HEADER_HEIGHT,
    backgroundColor: '#1a3579',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingTop: 40,
    paddingLeft: 10,
  },
  logo: {
    width: 150,
    height: 45,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
    color: '#000'
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 15,
    alignSelf: 'flex-start',
    width: '100%',
    maxWidth: 400,
    color: '#333'
  },
  input: {
    width: '100%',
    maxWidth: 400,
    height: 50,
    borderColor: '#000',
    borderWidth: 2,
    borderRadius: 25,
    paddingHorizontal: 16,
    marginBottom: 15,
    fontSize: 16,
    backgroundColor: '#fff'
  },
  row: {
    flexDirection: 'row',
    width: '100%',
    maxWidth: 400,
    justifyContent: 'space-between',
  },
  thirdInput: {
    width: '30%',
    maxWidth: 120,
  },
  nfcRow: {
    flexDirection: 'row',
    width: '100%',
    maxWidth: 400,
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  nfcButton: {
    width: '48%',
    backgroundColor: '#fff',
    borderColor: '#000',
    borderWidth: 2,
    borderRadius: 25,
    alignItems: 'center',
    paddingVertical: 15,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  cameraButton: {
    backgroundColor: '#fff',
  },
  nfcIcon: {
    fontSize: 16,
    marginRight: 5,
  },
  nfcText: {
    color: '#000',
    fontWeight: '600',
    fontSize: 14,
  },
  cardPreview: {
    width: '100%',
    maxWidth: 400,
    height: 200,
    backgroundColor: '#e0e0e0',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardChip: {
    width: 40,
    height: 30,
    backgroundColor: '#333',
    borderRadius: 5,
  },
  visaText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a73e8',
  },
  cardNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    letterSpacing: 2,
  },
  cardName: {
    fontSize: 14,
    color: '#666',
  },
  paymentButtons: {
    width: '100%',
    maxWidth: 400,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  payButton: {
    width: '48%',
    backgroundColor: '#e53935',
    borderRadius: 12,
    alignItems: 'center',
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  bkmButton: {
    width: '48%',
    backgroundColor: '#e53935',
    borderRadius: 12,
    alignItems: 'center',
    paddingVertical: 16,
  },
  lockIcon: {
    color: '#fff',
    fontSize: 16,
    marginRight: 5,
  },
  payButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  bkmText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
    textAlign: 'center',
  }
});
