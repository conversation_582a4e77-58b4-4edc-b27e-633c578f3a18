import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { Dimensions, Image, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

const { width } = Dimensions.get('window');

export default function RegisterScreen() {
  const router = useRouter();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="light-content" backgroundColor="#1a3579" />
      <View style={styles.container}>
        {/* ÜST MAVİ BAR ve LOGO */}
        <View style={styles.header}>
          <Image
            source={require('../assets/images/logo_light.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        {/* CONTENT AREA */}
        <View style={styles.content}>
          {/* KAYIT OL BAŞLIK */}
          <Text style={styles.title}>Kayıt Ol</Text>

          {/* INPUTLAR */}
          <TextInput style={styles.input} placeholder="Ad Soyad" value={name} onChangeText={setName} />
          <TextInput style={styles.input} placeholder="E-posta" value={email} onChangeText={setEmail} autoCapitalize="none" />
          <TextInput style={styles.input} placeholder="Şifre" value={password} onChangeText={setPassword} secureTextEntry />

          {/* KAYIT OL BUTONU */}
          <TouchableOpacity style={styles.button} onPress={() => router.push('/musteri-bilgileri')}>
            <Text style={styles.buttonText}>Kayıt Ol</Text>
          </TouchableOpacity>

          {/* ALTTAKİ SATIR */}
          <View style={styles.row}>
            <Text style={styles.info}>Zaten Hesabın var mı? </Text>
            <Text style={styles.link} onPress={() => router.push('/login')}>Giriş Yap</Text>
          </View>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16
  },
  logo: {
    width: 160,
    height: 60,
    resizeMode: 'contain',
    marginBottom: 8
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20
  },
  input: {
    width: '100%',
    maxWidth: 340,
    height: 48,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 14,
    marginBottom: 16,
    fontSize: 16
  },
  button: {
    width: '100%',
    maxWidth: 340,
    backgroundColor: '#e53935',
    borderRadius: 8,
    alignItems: 'center',
    padding: 14,
    marginTop: 6
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18
  },
  row: {
    flexDirection: 'row',
    marginTop: 18,
    alignItems: 'center',
    justifyContent: 'center'
  },
  info: {
    color: '#e53935',
    fontSize: 15,
    fontWeight: '400'
  },
  link: {
    color: '#1a3579',
    fontWeight: 'bold',
    fontSize: 16
  }
});
