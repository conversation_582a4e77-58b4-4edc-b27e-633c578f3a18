// app/index.tsx
import React, { useEffect, useState } from 'react';
import { View, Image, StatusBar, StyleSheet } from 'react-native';
import { router } from 'expo-router';

export default function Index() {
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    const t = setTimeout(() => {
      setShowSplash(false);
      router.replace('/login'); // login rotasına geç
    }, 2000);
    return () => clearTimeout(t);
  }, []);

  if (showSplash) {
    return (
      <View style={styles.container}>
        <StatusBar hidden />
        <Image
          source={require('../assets/images/logo_light.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
    );
  }

  // Splash bittiğinde zaten router.replace ile login'e gidecek
  return null;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1C2C7E',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: { width: '100%', height: '100%' },
});
