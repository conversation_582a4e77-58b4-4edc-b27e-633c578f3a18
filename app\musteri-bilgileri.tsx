import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { Dimensions, Image, ScrollView, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

const { width } = Dimensions.get('window');

export default function MusteriBilgileriScreen() {
  const router = useRouter();
  const [firma, setFirma] = useState('');
  const [musteri, setMusteri] = useState('');
  const [telefon, setTelefon] = useState('');
  const [email, setEmail] = useState('');
  const [urun, setUrun] = useState('');
  const [not, setNot] = useState('');
  const [adres, setAdres] = useState('');

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="light-content" backgroundColor="#1a3579" />
      <View style={styles.container}>
        {/* ÜST MAVİ BAR ve LOGO */}
        <View style={styles.header}>
          <Image
            source={require('../assets/images/logo_light.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        {/* CONTENT AREA */}
        <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer} keyboardShouldPersistTaps="handled">
          {/* MÜŞTERİ BİLGİLERİ BAŞLIK */}
          <Text style={styles.title}>Müşteri Bilgileri</Text>

          {/* INPUTLAR */}
          <TextInput style={styles.input} placeholder="Firma Adı" value={firma} onChangeText={setFirma} />

          <View style={styles.row}>
            <TextInput style={[styles.input, styles.halfInput]} placeholder="Müşteri Adı*" value={musteri} onChangeText={setMusteri} />
            <TextInput style={[styles.input, styles.halfInput]} placeholder="Telefon No*" value={telefon} onChangeText={setTelefon} keyboardType="phone-pad" />
          </View>

          <TextInput style={styles.input} placeholder="E-Posta Adresi*" value={email} onChangeText={setEmail} autoCapitalize="none" />
          <TextInput style={styles.input} placeholder="Ürün/Hizmet Seç*" value={urun} onChangeText={setUrun} />
          <TextInput style={styles.input} placeholder="Notunuz*" value={not} onChangeText={setNot} />
          <TextInput style={styles.input} placeholder="Adresiniz*" value={adres} onChangeText={setAdres} />

          {/* ÖDEME BUTONU */}
          <TouchableOpacity style={styles.button} onPress={() => router.push('/odeme')}>
            <Text style={styles.buttonText}>Ödeme Ekranına git</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </>
  );
}

const HEADER_HEIGHT = 120;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    width: width,
    height: HEADER_HEIGHT,
    backgroundColor: '#1a3579',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingTop: 20,
    paddingLeft: 20,
  },
  logo: {
    width: 150,
    height: 45,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
    color: '#000'
  },
  input: {
    width: '100%',
    maxWidth: 400,
    height: 50,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 15,
    fontSize: 16,
    backgroundColor: '#fff'
  },
  row: {
    flexDirection: 'row',
    width: '100%',
    maxWidth: 400,
    justifyContent: 'space-between',
  },
  halfInput: {
    width: '48%',
    maxWidth: 190,
  },
  button: {
    width: '100%',
    maxWidth: 400,
    backgroundColor: '#e53935',
    borderRadius: 12,
    alignItems: 'center',
    paddingVertical: 16,
    marginTop: 20,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18
  }
});
