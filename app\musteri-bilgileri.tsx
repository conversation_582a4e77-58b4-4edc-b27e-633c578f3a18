import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Image, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';

export default function MusteriBilgileriScreen() {
  const router = useRouter();
  const [firma, setFirma] = useState('');
  const [musteri, setMusteri] = useState('');
  const [telefon, setTelefon] = useState('');
  const [email, setEmail] = useState('');
  const [urun, setUrun] = useState('');
  const [not, setNot] = useState('');
  const [adres, setAdres] = useState('');

  return (
    <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
      <Image source={require('../assets/images/logo_light.png')} style={styles.logo} />
      <Text style={styles.title}>Müşteri Bilgileri</Text>
      <TextInput style={styles.input} placeholder="Firma Adı" value={firma} onChangeText={setFirma} />
      <TextInput style={styles.input} placeholder="Müşteri Adı*" value={musteri} onChangeText={setMusteri} />
      <TextInput style={styles.input} placeholder="Telefon No*" value={telefon} onChangeText={setTelefon} keyboardType="phone-pad" />
      <TextInput style={styles.input} placeholder="E-Posta Adresi*" value={email} onChangeText={setEmail} autoCapitalize="none" />
      <TextInput style={styles.input} placeholder="Ürün/Hizmet Seç*" value={urun} onChangeText={setUrun} />
      <TextInput style={styles.input} placeholder="Notunuz" value={not} onChangeText={setNot} />
      <TextInput style={styles.input} placeholder="Adresiniz" value={adres} onChangeText={setAdres} />
      <TouchableOpacity style={styles.button} onPress={() => router.push('/odeme')}>
        <Text style={styles.buttonText}>Ödeme Ekranına Git</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flexGrow:1, backgroundColor:'#fff', alignItems:'center', justifyContent:'center', padding:16 },
  logo: { width: 160, height: 60, resizeMode: 'contain', marginBottom: 8 },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 10 },
  input: { width:'100%', maxWidth:340, height:48, borderColor:'#ccc', borderWidth:1, borderRadius:8, paddingHorizontal:14, marginBottom:10, fontSize:16 },
  button: { width:'100%', maxWidth:340, backgroundColor:'#e53935', borderRadius:8, alignItems:'center', padding:14, marginTop:10 },
  buttonText: { color:'#fff', fontWeight:'bold', fontSize:18 }
});
